import json
import xml.etree.ElementTree as ET
import xml.dom.minidom as minidom

def parse_json_to_vif(json_file, output_vif_file):
    # Load JSON data
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    # Create VIF XML structure with proper namespace prefix
    vif_root = ET.Element("vif:VIF")
    vif_root.set("xmlns:opt", "http://usb.org/VendorInfoFileOptionalContent.xsd")
    vif_root.set("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance")
    vif_root.set("xmlns:vif", "http://usb.org/VendorInfoFile.xsd")
    
    # Add VIF specification version
    spec = ET.SubElement(vif_root, "vif:VIF_Specification")
    spec.text = "3.34"
    
    # Add VIF app info
    app = ET.SubElement(vif_root, "vif:VIF_App")
    vendor = ET.SubElement(app, "vif:Vendor")
    vendor.text = "USB-IF"
    name = ET.SubElement(app, "vif:Name")
    name.text = "VIF Editor"
    version = ET.SubElement(app, "vif:Version")
    version.text = "3.12.0.0"
    
    # Extract metadata from JSON
    metadata = data.get("metadata", {})
    device_info = metadata.get("device supported", [])[0] if metadata.get("device supported") else {}
    
    # Add vendor name
    vendor_name = ET.SubElement(vif_root, "vif:Vendor_Name")
    vendor_name.text = "TI"
    
    # Add model part number
    model = ET.SubElement(vif_root, "vif:Model_Part_Number")
    model.text = device_info.get("variant", "TPS66994")
    
    # Add product revision
    revision = ET.SubElement(vif_root, "vif:Product_Revision")
    revision.text = device_info.get("endEquipment", "DEVICE")
    
    # Add TID
    tid = ET.SubElement(vif_root, "vif:TID")
    tid.text = "UFP_EPR_LM"
    
    # Add VIF product type
    product_type = ET.SubElement(vif_root, "vif:VIF_Product_Type")
    product_type.set("value", "0")
    product_type.text = "Port Product"
    
    # Add certification type
    cert_type = ET.SubElement(vif_root, "vif:Certification_Type")
    cert_type.set("value", "0")
    cert_type.text = "End Product"
    
    # Add Product section with USB4 router configuration
    product = ET.SubElement(vif_root, "vif:Product")
    
    # Add USB4 product level content
    product.append(ET.Comment("Product Level Content:"))
    product.append(ET.Comment(";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"))
    product.append(ET.Comment(";USB4® Product"))
    product.append(ET.Comment(";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"))
    
    # USB4 DROM Vendor ID
    usb4_drom_vid = ET.SubElement(product, "vif:USB4_DROM_Vendor_ID")
    usb4_drom_vid.set("value", "4369")
    usb4_drom_vid.text = "1111"
    
    # USB4 Device settings
    usb4_hifi = ET.SubElement(product, "vif:USB4_Device_HiFi_Bi_TMU_Mode_Required")
    usb4_hifi.set("value", "false")
    
    usb4_host_ctrl = ET.SubElement(product, "vif:USB4_Num_Internal_Host_Controllers")
    usb4_host_ctrl.set("value", "1")
    usb4_host_ctrl.text = "1 host controller"
    
    # Add more USB4 settings...
    usb4_pcie_bridges = ET.SubElement(product, "vif:USB4_Num_PCIe_DN_Bridges")
    usb4_pcie_bridges.set("value", "0")
    usb4_pcie_bridges.text = "0 PCIe DN bridges"
    
    # USB4 supported protocols
    for protocol in ["Audio", "HID", "Printer", "Mass_Storage", "Video", "Comms_Networking", 
                     "Media_Transfer_Protocol", "Smart_Card", "Still_Image_Capture", "Monitor_Device"]:
        elem = ET.SubElement(product, f"vif:USB4_{protocol}_Supported")
        elem.set("value", "false")
    
    # USB4 Router List
    router_list = ET.SubElement(product, "vif:USB4RouterList")
    router = ET.SubElement(router_list, "vif:Usb4Router")
    router.append(ET.Comment("USB4 Router 0"))
    
    # Router configuration
    router_spec = ET.SubElement(router, "vif:USB4_Spec_Version")
    router_spec.set("value", "1")
    router_spec.text = "USB4 Version 2.0"
    
    router_id = ET.SubElement(router, "vif:USB4_Router_ID")
    router_id.set("value", "0")
    router_id.text = "0"
    
    # Add more router settings...
    
    # Add Component sections for each port
    num_ports = device_info.get("ports", 2)
    for port_idx in range(num_ports):
        component = ET.SubElement(vif_root, "vif:Component")
        component.append(ET.Comment(f"Component {port_idx}: Port {port_idx}"))
        
        # Port configuration
        port_label = ET.SubElement(component, "vif:Port_Label")
        port_label.text = str(port_idx)
        
        connector_type = ET.SubElement(component, "vif:Connector_Type")
        connector_type.set("value", "2")
        connector_type.text = "Type-C®"
        
        # USB4 support
        usb4_supported = ET.SubElement(component, "vif:USB4_Supported")
        usb4_supported.set("value", "true")
        
        # PD configuration
        pd_support = ET.SubElement(component, "vif:USB_PD_Support")
        pd_support.set("value", "true")
        
        pd_port_type = ET.SubElement(component, "vif:PD_Port_Type")
        pd_port_type.set("value", "4")
        pd_port_type.text = "DRP"
        
        # Add PD Source PDOs with detailed configuration
        pd_power = ET.SubElement(component, "vif:PD_Power_As_Source")
        pd_power.set("value", "240000")
        pd_power.text = "240000 mW"
        
        epr_supported = ET.SubElement(component, "vif:EPR_Supported_As_Src")
        epr_supported.text = "true"
        
        num_src_pdos = ET.SubElement(component, "vif:Num_Src_PDOs")
        num_src_pdos.set("value", "9")
        num_src_pdos.text = "9 Src PDOs"
        
        # Source PDO List
        src_pdo_list = ET.SubElement(component, "vif:SrcPdoList")
        
        # Define PDO configurations based on the original VIF
        pdos = [
            {"voltage": 100, "current": 300, "type": "Fixed", "comment": "Source PDO 1"},
            {"voltage": 180, "current": 300, "type": "Fixed", "comment": "Source PDO 2"},
            {"voltage": 300, "current": 300, "type": "Fixed", "comment": "Source PDO 3"},
            {"voltage": 400, "current": 500, "type": "Fixed", "comment": "Source PDO 4"},
            {"min_voltage": 150, "max_voltage": 210, "current": 500, "type": "Augmented", "comment": "Source PDO 5"},
            {"voltage": 560, "current": 500, "type": "Fixed", "comment": "Source PDO 6"},
            {"min_voltage": 150, "max_voltage": 280, "current": 500, "type": "Augmented", "comment": "Source PDO 7"},
            {"voltage": 960, "current": 500, "type": "Fixed", "comment": "Source PDO 8"},
            {"min_voltage": 150, "max_voltage": 480, "pdp": 240, "type": "EPR_Augmented", "comment": "Source PDO 9"}
        ]
        
        for i, pdo_config in enumerate(pdos):
            src_pdo = ET.SubElement(src_pdo_list, "vif:SrcPDO")
            src_pdo.append(ET.Comment(pdo_config["comment"]))
            
            if pdo_config["type"] == "Fixed":
                supply_type = ET.SubElement(src_pdo, "vif:Src_PDO_Supply_Type")
                supply_type.set("value", "0")
                supply_type.text = "Fixed"
                
                peak_current = ET.SubElement(src_pdo, "vif:Src_PDO_Peak_Current")
                peak_current.set("value", "0")
                peak_current.text = "100% IOC"
                
                voltage = ET.SubElement(src_pdo, "vif:Src_PDO_Voltage")
                voltage.set("value", str(pdo_config["voltage"]))
                voltage.text = f"{pdo_config['voltage'] * 50} mV (Factor = 50)"
                
                max_current = ET.SubElement(src_pdo, "vif:Src_PDO_Max_Current")
                max_current.set("value", str(pdo_config["current"]))
                max_current.text = f"{pdo_config['current'] * 10} mA (Factor = 10)"
                
            elif pdo_config["type"] == "Augmented":
                supply_type = ET.SubElement(src_pdo, "vif:Src_PDO_Supply_Type")
                supply_type.set("value", "3")
                supply_type.text = "Augmented"
                
                apdo_type = ET.SubElement(src_pdo, "vif:Src_PDO_APDO_Type")
                apdo_type.set("value", "0")
                apdo_type.text = "Programmable Power Supply (PPS)"
                
                min_voltage = ET.SubElement(src_pdo, "vif:Src_PDO_Min_Voltage")
                min_voltage.set("value", str(pdo_config["min_voltage"]))
                min_voltage.text = f"{pdo_config['min_voltage'] * 100} mV (Factor = 100)"
                
                max_voltage = ET.SubElement(src_pdo, "vif:Src_PDO_Max_Voltage")
                max_voltage.set("value", str(pdo_config["max_voltage"]))
                max_voltage.text = f"{pdo_config['max_voltage'] * 100} mV (Factor = 100)"
                
            elif pdo_config["type"] == "EPR_Augmented":
                supply_type = ET.SubElement(src_pdo, "vif:Src_PDO_Supply_Type")
                supply_type.set("value", "3")
                supply_type.text = "Augmented"
                
                apdo_type = ET.SubElement(src_pdo, "vif:Src_PDO_APDO_Type")
                apdo_type.set("value", "1")
                apdo_type.text = "EPR Adjustable Voltage Supply (EPR AVS)"
                
                pdp_rating = ET.SubElement(src_pdo, "vif:Src_PDO_PDP_Rating")
                pdp_rating.set("value", str(pdo_config["pdp"]))
                pdp_rating.text = f"{pdo_config['pdp']} W"
        
        # Add Sink PDO configuration
        snk_power = ET.SubElement(component, "vif:PD_Power_As_Sink")
        snk_power.set("value", "0")
        snk_power.text = "0 mW"
        
        # Add SVID configurations
        modal_op = ET.SubElement(component, "vif:Modal_Operation_Supported_SOP")
        modal_op.set("value", "true")
        
        # Add cable VDM configuration
        # ... (add remaining cable and SOP configurations)
    
    # Format and write XML
    rough_string = ET.tostring(vif_root, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    
    # Custom formatting to match original
    pretty_xml = reparsed.toprettyxml(indent="  ", encoding="utf-8").decode('utf-8')
    
    # Clean up extra whitespace
    lines = [line for line in pretty_xml.split('\n') if line.strip()]
    formatted_xml = '\n'.join(lines)
    
    with open(output_vif_file, 'w', encoding='utf-8') as f:
        f.write(formatted_xml)
    
    return output_vif_file

# Example usage
if __name__ == "__main__":
    json_file = "TPS66994_Bovina_Creek_LM_EVM_UP.json"
    output_vif_file = "TPS66994_Bovina_Creek_LM_EVM_UP.xml"
    parse_json_to_vif(json_file, output_vif_file)
