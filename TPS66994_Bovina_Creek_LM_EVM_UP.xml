<?xml version="1.0" encoding="utf-8"?>
<vif:VIF xmlns:opt="http://usb.org/VendorInfoFileOptionalContent.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:vif="http://usb.org/VendorInfoFile.xsd">
  <vif:VIF_Specification>3.34</vif:VIF_Specification>
  <vif:VIF_App>
    <vif:Vendor>USB-IF</vif:Vendor>
    <vif:Name>VIF Editor</vif:Name>
    <vif:Version>3.12.0.0</vif:Version>
  </vif:VIF_App>
  <vif:Vendor_Name>TI</vif:Vendor_Name>
  <vif:Model_Part_Number>TPS66994</vif:Model_Part_Number>
  <vif:Product_Revision>Docking/Device</vif:Product_Revision>
  <vif:TID>UFP_EPR_LM</vif:TID>
  <vif:VIF_Product_Type value="0">Port Product</vif:VIF_Product_Type>
  <vif:Certification_Type value="0">End Product</vif:Certification_Type>
  <vif:Product>
    <!--Product Level Content:-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;USB4® Product-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:USB4_DROM_Vendor_ID value="4369">1111</vif:USB4_DROM_Vendor_ID>
    <vif:USB4_Device_HiFi_Bi_TMU_Mode_Required value="false" />
    <vif:USB4_Num_Internal_Host_Controllers value="1">1 host controller</vif:USB4_Num_Internal_Host_Controllers>
    <vif:USB4_Num_PCIe_DN_Bridges value="0">0 PCIe DN bridges</vif:USB4_Num_PCIe_DN_Bridges>
    <vif:USB4_Audio_Supported value="false" />
    <vif:USB4_HID_Supported value="false" />
    <vif:USB4_Printer_Supported value="false" />
    <vif:USB4_Mass_Storage_Supported value="false" />
    <vif:USB4_Video_Supported value="false" />
    <vif:USB4_Comms_Networking_Supported value="false" />
    <vif:USB4_Media_Transfer_Protocol_Supported value="false" />
    <vif:USB4_Smart_Card_Supported value="false" />
    <vif:USB4_Still_Image_Capture_Supported value="false" />
    <vif:USB4_Monitor_Device_Supported value="false" />
    <!--Bundle: USB4RouterList-->
    <vif:USB4RouterList>
      <vif:Usb4Router>
        <!--USB4 Router 0-->
        <vif:USB4_Spec_Version value="1">USB4 Version 2.0</vif:USB4_Spec_Version>
        <vif:USB4_Router_ID value="0">0</vif:USB4_Router_ID>
        <vif:USB4_Silicon_VID value="4369">1111</vif:USB4_Silicon_VID>
        <vif:USB4_Num_Lane_Adapters value="11">11 lane adapters</vif:USB4_Num_Lane_Adapters>
        <vif:USB4_Num_DP_IN_Adapters value="1">1 DP IN adapter</vif:USB4_Num_DP_IN_Adapters>
        <vif:USB4_Num_DP_OUT_Adapters value="1">1 DP OUT adapter</vif:USB4_Num_DP_OUT_Adapters>
        <vif:USB4_Num_PCIe_DN_Adapters value="1">1 PCIe DN adapter</vif:USB4_Num_PCIe_DN_Adapters>
        <vif:USB4_TBT3_Not_Supported value="0">TBT3 Compatible</vif:USB4_TBT3_Not_Supported>
        <vif:USB4_PCIe_Wake_Supported value="false" />
        <vif:USB4_USB3_Wake_Supported value="false" />
        <vif:USB4_Num_Unused_Adapters value="1">1 unused adapter</vif:USB4_Num_Unused_Adapters>
        <vif:USB4_TBT3_VID value="4369">1111</vif:USB4_TBT3_VID>
        <vif:USB4_PCIe_Switch_Vendor_ID value="4369">1111</vif:USB4_PCIe_Switch_Vendor_ID>
        <vif:USB4_PCIe_Switch_Device_ID value="4369">1111</vif:USB4_PCIe_Switch_Device_ID>
        <vif:USB4_Time_Sync_Protocol_Supported value="false" />
        <vif:USB4_ID_Time_Sync_Supported value="false" />
        <vif:USB4_Num_Vendor_Specific_Adapters value="1">1 vendor specific adapter</vif:USB4_Num_Vendor_Specific_Adapters>
        <vif:USB4_PCIe_MPS value="256">256 bytes</vif:USB4_PCIe_MPS>
        <vif:USB4_PCIe_Read_Request_Size value="1">1 byte</vif:USB4_PCIe_Read_Request_Size>
        <vif:USB4_PCIe_Write_Request_Size value="1">1 byte</vif:USB4_PCIe_Write_Request_Size>
        <vif:USB4_Num_PCIe_Endpoints value="0">0 PCIe endpoints</vif:USB4_Num_PCIe_Endpoints>
      </vif:Usb4Router>
    </vif:USB4RouterList>
  </vif:Product>
  <vif:Component>
    <!--Component 0: Port 0-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;Component-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:Port_Label>0</vif:Port_Label>
    <vif:Connector_Type value="2">Type-C®</vif:Connector_Type>
    <vif:USB4_Supported value="true" />
    <vif:USB4_Router_Index value="0">0</vif:USB4_Router_Index>
    <vif:USB_PD_Support value="true" />
    <vif:PD_Port_Type value="4">DRP</vif:PD_Port_Type>
    <vif:Type_C_State_Machine value="2">DRP</vif:Type_C_State_Machine>
    <vif:Port_Battery_Powered value="false" />
    <vif:BC_1_2_Support value="0">None</vif:BC_1_2_Support>
    <vif:Captive_Cable value="true" />
    <vif:Captive_Cable_Is_eMarked value="true" />
    <vif:PD_Controller_Integrated_eMarker value="false" />
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;General PD-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:PD_Spec_Revision_Major value="3">3</vif:PD_Spec_Revision_Major>
    <vif:PD_Spec_Revision_Minor value="2">2</vif:PD_Spec_Revision_Minor>
    <vif:PD_Spec_Version_Major value="1">1</vif:PD_Spec_Version_Major>
    <vif:PD_Spec_Version_Minor value="0">0</vif:PD_Spec_Version_Minor>
    <vif:PD_Specification_Revision value="2">Revision 3</vif:PD_Specification_Revision>
    <vif:SOP_Capable value="true" />
    <vif:SOP_P_Capable value="true" />
    <vif:SOP_PP_Capable value="false" />
    <vif:SOP_P_Debug_Capable value="false" />
    <vif:SOP_PP_Debug_Capable value="false" />
    <vif:Manufacturer_Info_Supported_Port value="true" />
    <vif:Manufacturer_Info_VID_Port value="1105">0451</vif:Manufacturer_Info_VID_Port>
    <vif:Manufacturer_Info_PID_Port value="1">0001</vif:Manufacturer_Info_PID_Port>
    <vif:Chunking_Implemented_SOP value="true" />
    <vif:Unchunked_Extended_Messages_Supported value="true" />
    <vif:Security_Msgs_Supported_SOP value="false" />
    <vif:Unconstrained_Power value="true" />
    <vif:Num_Fixed_Batteries value="0">0</vif:Num_Fixed_Batteries>
    <vif:Num_Swappable_Battery_Slots value="0">0</vif:Num_Swappable_Battery_Slots>
    <vif:ID_Header_Connector_Type_SOP value="2">USB Type-C® Receptacle</vif:ID_Header_Connector_Type_SOP>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;PD Capabilities-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:USB_Comms_Capable value="true" />
    <vif:DR_Swap_To_DFP_Supported value="false" />
    <vif:DR_Swap_To_UFP_Supported value="true" />
    <vif:VCONN_Swap_To_On_Supported value="false" />
    <vif:VCONN_Swap_To_Off_Supported value="false" />
    <vif:Responds_To_Discov_SOP_UFP value="true" />
    <vif:Responds_To_Discov_SOP_DFP value="true" />
    <vif:Attempts_Discov_SOP value="true" />
    <vif:Power_Interruption_Available value="0">No Interruption Possible</vif:Power_Interruption_Available>
    <vif:Data_Reset_Supported value="true" />
    <vif:Enter_USB_Supported value="false" />
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;USB Type-C®-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:Type_C_Can_Act_As_Host value="false" />
    <vif:Type_C_Can_Act_As_Device value="true" />
    <vif:Type_C_Implements_Try_SRC value="false" />
    <vif:Type_C_Implements_Try_SNK value="false" />
    <vif:Type_C_Supports_Audio_Accessory value="false" />
    <vif:Type_C_Is_VCONN_Powered_Accessory value="false" />
    <vif:Type_C_Is_Debug_Target_SRC value="true" />
    <vif:Type_C_Is_Debug_Target_SNK value="false" />
    <vif:RP_Value value="2">3A</vif:RP_Value>
    <vif:Type_C_Port_On_Hub value="true" />
    <vif:Type_C_Power_Source value="2">Both</vif:Type_C_Power_Source>
    <vif:Type_C_Sources_VCONN value="false" />
    <vif:Type_C_Is_Alt_Mode_Controller value="false" />
    <vif:Type_C_Is_Alt_Mode_Adapter value="true" />
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;USB4® Port-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:USB4_Lane_0_Adapter value="1">1</vif:USB4_Lane_0_Adapter>
    <vif:USB4_Max_Speed value="1">Gen 3 (40Gb)</vif:USB4_Max_Speed>
    <vif:USB4_DFP_Supported value="false" />
    <vif:USB4_UFP_Supported value="true" />
    <vif:USB4_USB3_Tunneling_Supported value="false" />
    <vif:USB4_DP_Tunneling_Supported value="true" />
    <vif:USB4_PCIe_Tunneling_Supported value="true" />
    <vif:USB4_TBT3_Compatibility_Supported value="true" />
    <vif:USB4_CL0s_State_Supported value="false" />
    <vif:USB4_CL1_State_Supported value="false" />
    <vif:USB4_CL2_State_Supported value="false" />
    <vif:USB4_Num_Retimers value="1">1 retimer</vif:USB4_Num_Retimers>
    <vif:USB4_DP_Bit_Rate value="3">HBR3</vif:USB4_DP_Bit_Rate>
    <vif:USB4_UHBR13_5_Supported value="false" />
    <vif:USB4_Num_DP_Lanes value="4">4 Lanes</vif:USB4_Num_DP_Lanes>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;Product Power-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:Product_Total_Source_Power_mW value="240000">240000 mW</vif:Product_Total_Source_Power_mW>
    <vif:Port_Source_Power_Type value="0">Assured</vif:Port_Source_Power_Type>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;USB Device-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:Device_Supports_USB_Data value="true" />
    <vif:Device_Speed value="0">USB 2</vif:Device_Speed>
    <vif:Device_Max_USB2_Speed value="0">Low Speed</vif:Device_Max_USB2_Speed>
    <vif:Device_Contains_Captive_Retimer value="false" />
    <vif:Hub_Billboard_DSPort_Num value="1">0001</vif:Hub_Billboard_DSPort_Num>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;PD Source-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:PD_Power_As_Source value="240000">240000 mW</vif:PD_Power_As_Source>
    <vif:EPR_Supported_As_Src value="true" />
    <vif:USB_Suspend_May_Be_Cleared value="true" />
    <vif:Sends_Pings value="false" />
    <vif:FR_Swap_Type_C_Current_Capability_As_Initial_Sink value="0">FR_Swap not supported</vif:FR_Swap_Type_C_Current_Capability_As_Initial_Sink>
    <vif:Master_Port value="true" />
    <vif:Has_Invariant_PDOs value="true" />
    <vif:Port_Managed_Guaranteed_Type value="1">Guaranteed Capability</vif:Port_Managed_Guaranteed_Type>
    <vif:DPS_Supported value="false" />
    <vif:Num_Src_PDOs value="9">9 Src PDOs</vif:Num_Src_PDOs>
    <vif:PD_OC_Protection value="true" />
    <vif:PD_OCP_Method value="2">Both</vif:PD_OCP_Method>
    <!--Bundle: SrcPdoList-->
    <vif:SrcPdoList>
      <vif:SrcPDO>
        <!--Source PDO 1-->
        <vif:Src_PDO_Supply_Type value="0">Fixed</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Voltage value="100">5000 mV (Factor = 50)</vif:Src_PDO_Voltage>
        <vif:Src_PDO_Max_Current value="300">3000 mA (Factor = 10)</vif:Src_PDO_Max_Current>
        <vif:Src_PD_OCP_OC_Debounce value="10">10 msec</vif:Src_PD_OCP_OC_Debounce>
        <vif:Src_PD_OCP_OC_Threshold value="350">3500 mA (Factor = 10)</vif:Src_PD_OCP_OC_Threshold>
        <vif:Src_PD_OCP_UV_Debounce value="10">10 msec</vif:Src_PD_OCP_UV_Debounce>
        <vif:Src_PD_OCP_UV_Threshold_Type value="1">Percentage</vif:Src_PD_OCP_UV_Threshold_Type>
        <vif:Src_PD_OCP_UV_Threshold value="20">20 %</vif:Src_PD_OCP_UV_Threshold>
      </vif:SrcPDO>
      <vif:SrcPDO>
        <!--Source PDO 2-->
        <vif:Src_PDO_Supply_Type value="0">Fixed</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Voltage value="180">9000 mV (Factor = 50)</vif:Src_PDO_Voltage>
        <vif:Src_PDO_Max_Current value="300">3000 mA (Factor = 10)</vif:Src_PDO_Max_Current>
        <vif:Src_PD_OCP_OC_Debounce value="10">10 msec</vif:Src_PD_OCP_OC_Debounce>
        <vif:Src_PD_OCP_OC_Threshold value="350">3500 mA (Factor = 10)</vif:Src_PD_OCP_OC_Threshold>
        <vif:Src_PD_OCP_UV_Debounce value="10">10 msec</vif:Src_PD_OCP_UV_Debounce>
        <vif:Src_PD_OCP_UV_Threshold_Type value="1">Percentage</vif:Src_PD_OCP_UV_Threshold_Type>
        <vif:Src_PD_OCP_UV_Threshold value="20">20 %</vif:Src_PD_OCP_UV_Threshold>
      </vif:SrcPDO>
      <vif:SrcPDO>
        <!--Source PDO 3-->
        <vif:Src_PDO_Supply_Type value="0">Fixed</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Voltage value="300">15000 mV (Factor = 50)</vif:Src_PDO_Voltage>
        <vif:Src_PDO_Max_Current value="300">3000 mA (Factor = 10)</vif:Src_PDO_Max_Current>
        <vif:Src_PD_OCP_OC_Debounce value="10">10 msec</vif:Src_PD_OCP_OC_Debounce>
        <vif:Src_PD_OCP_OC_Threshold value="350">3500 mA (Factor = 10)</vif:Src_PD_OCP_OC_Threshold>
        <vif:Src_PD_OCP_UV_Debounce value="10">10 msec</vif:Src_PD_OCP_UV_Debounce>
        <vif:Src_PD_OCP_UV_Threshold_Type value="1">Percentage</vif:Src_PD_OCP_UV_Threshold_Type>
        <vif:Src_PD_OCP_UV_Threshold value="20">20 %</vif:Src_PD_OCP_UV_Threshold>
      </vif:SrcPDO>
      <vif:SrcPDO>
        <!--Source PDO 4-->
        <vif:Src_PDO_Supply_Type value="0">Fixed</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Voltage value="400">20000 mV (Factor = 50)</vif:Src_PDO_Voltage>
        <vif:Src_PDO_Max_Current value="500">5000 mA (Factor = 10)</vif:Src_PDO_Max_Current>
        <vif:Src_PD_OCP_OC_Debounce value="10">10 msec</vif:Src_PD_OCP_OC_Debounce>
        <vif:Src_PD_OCP_OC_Threshold value="550">5500 mA (Factor = 10)</vif:Src_PD_OCP_OC_Threshold>
        <vif:Src_PD_OCP_UV_Debounce value="10">10 msec</vif:Src_PD_OCP_UV_Debounce>
        <vif:Src_PD_OCP_UV_Threshold_Type value="1">Percentage</vif:Src_PD_OCP_UV_Threshold_Type>
        <vif:Src_PD_OCP_UV_Threshold value="10">10 %</vif:Src_PD_OCP_UV_Threshold>
      </vif:SrcPDO>
      <vif:SrcPDO>
        <!--Source PDO 5-->
        <vif:Src_PDO_Supply_Type value="3">Augmented</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_APDO_Type value="2">SPR Adjustable Voltage Supply (SPR AVS)</vif:Src_PDO_APDO_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Max_Current_9V_To_15V value="300">3000 mA (Factor = 10)</vif:Src_PDO_Max_Current_9V_To_15V>
        <vif:Src_PDO_Max_Current_15V_To_20V value="500">5000 mA (Factor = 10)</vif:Src_PDO_Max_Current_15V_To_20V>
      </vif:SrcPDO>
      <vif:SrcPDO>
        <!--Source PDO 6-->
        <vif:Src_PDO_Supply_Type value="0">Fixed</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Voltage value="560">28000 mV (Factor = 50)</vif:Src_PDO_Voltage>
        <vif:Src_PDO_Max_Current value="500">5000 mA (Factor = 10)</vif:Src_PDO_Max_Current>
        <vif:Src_PD_OCP_OC_Debounce value="10">10 msec</vif:Src_PD_OCP_OC_Debounce>
        <vif:Src_PD_OCP_OC_Threshold value="530">5300 mA (Factor = 10)</vif:Src_PD_OCP_OC_Threshold>
        <vif:Src_PD_OCP_UV_Debounce value="10">10 msec</vif:Src_PD_OCP_UV_Debounce>
        <vif:Src_PD_OCP_UV_Threshold_Type value="1">Percentage</vif:Src_PD_OCP_UV_Threshold_Type>
        <vif:Src_PD_OCP_UV_Threshold value="20">20 %</vif:Src_PD_OCP_UV_Threshold>
      </vif:SrcPDO>
      <vif:SrcPDO>
        <!--Source PDO 7-->
        <vif:Src_PDO_Supply_Type value="0">Fixed</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Voltage value="720">36000 mV (Factor = 50)</vif:Src_PDO_Voltage>
        <vif:Src_PDO_Max_Current value="500">5000 mA (Factor = 10)</vif:Src_PDO_Max_Current>
        <vif:Src_PD_OCP_OC_Debounce value="10">10 msec</vif:Src_PD_OCP_OC_Debounce>
        <vif:Src_PD_OCP_OC_Threshold value="520">5200 mA (Factor = 10)</vif:Src_PD_OCP_OC_Threshold>
        <vif:Src_PD_OCP_UV_Debounce value="10">10 msec</vif:Src_PD_OCP_UV_Debounce>
        <vif:Src_PD_OCP_UV_Threshold_Type value="1">Percentage</vif:Src_PD_OCP_UV_Threshold_Type>
        <vif:Src_PD_OCP_UV_Threshold value="20">20 %</vif:Src_PD_OCP_UV_Threshold>
      </vif:SrcPDO>
      <vif:SrcPDO>
        <!--Source PDO 8-->
        <vif:Src_PDO_Supply_Type value="0">Fixed</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Voltage value="960">48000 mV (Factor = 50)</vif:Src_PDO_Voltage>
        <vif:Src_PDO_Max_Current value="500">5000 mA (Factor = 10)</vif:Src_PDO_Max_Current>
        <vif:Src_PD_OCP_OC_Debounce value="10">10 msec</vif:Src_PD_OCP_OC_Debounce>
        <vif:Src_PD_OCP_OC_Threshold value="510">5100 mA (Factor = 10)</vif:Src_PD_OCP_OC_Threshold>
        <vif:Src_PD_OCP_UV_Debounce value="10">10 msec</vif:Src_PD_OCP_UV_Debounce>
        <vif:Src_PD_OCP_UV_Threshold_Type value="1">Percentage</vif:Src_PD_OCP_UV_Threshold_Type>
        <vif:Src_PD_OCP_UV_Threshold value="20">20 %</vif:Src_PD_OCP_UV_Threshold>
      </vif:SrcPDO>
      <vif:SrcPDO>
        <!--Source PDO 9-->
        <vif:Src_PDO_Supply_Type value="3">Augmented</vif:Src_PDO_Supply_Type>
        <vif:Src_PDO_APDO_Type value="1">EPR Adjustable Voltage Supply (EPR AVS)</vif:Src_PDO_APDO_Type>
        <vif:Src_PDO_Peak_Current value="0">100% IOC</vif:Src_PDO_Peak_Current>
        <vif:Src_PDO_Min_Voltage value="150">15000 mV (Factor = 100)</vif:Src_PDO_Min_Voltage>
        <vif:Src_PDO_Max_Voltage value="480">48000 mV (Factor = 100)</vif:Src_PDO_Max_Voltage>
        <vif:Src_PDO_PDP_Rating value="240">240 W</vif:Src_PDO_PDP_Rating>
      </vif:SrcPDO>
    </vif:SrcPdoList>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;PD Sink-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:PD_Power_As_Sink value="0">0 mW</vif:PD_Power_As_Sink>
    <vif:EPR_Supported_As_Snk value="false" />
    <vif:No_USB_Suspend_May_Be_Set value="false" />
    <vif:GiveBack_May_Be_Set value="false" />
    <vif:Higher_Capability_Set value="false" />
    <vif:FR_Swap_Reqd_Type_C_Current_As_Initial_Source value="0">FR_Swap not supported</vif:FR_Swap_Reqd_Type_C_Current_As_Initial_Source>
    <vif:Num_Snk_PDOs value="1">1 Snk PDO</vif:Num_Snk_PDOs>
    <!--Bundle: SnkPdoList-->
    <vif:SnkPdoList>
      <vif:SnkPDO>
        <!--Sink PDO 1-->
        <vif:Snk_PDO_Supply_Type value="0">Fixed</vif:Snk_PDO_Supply_Type>
        <vif:Snk_PDO_Voltage value="100">5000 mV (Factor = 50)</vif:Snk_PDO_Voltage>
        <vif:Snk_PDO_Op_Current value="0">0 mA (Factor = 10)</vif:Snk_PDO_Op_Current>
      </vif:SnkPDO>
    </vif:SnkPdoList>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;Dual Role-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:Accepts_PR_Swap_As_Src value="true" />
    <vif:Accepts_PR_Swap_As_Snk value="true" />
    <vif:Requests_PR_Swap_As_Src value="false" />
    <vif:Requests_PR_Swap_As_Snk value="true" />
    <vif:FR_Swap_Supported_As_Initial_Sink value="false" />
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;SOP Discover ID-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:XID_SOP value="0">0</vif:XID_SOP>
    <vif:Data_Capable_As_USB_Host_SOP value="false" />
    <vif:Data_Capable_As_USB_Device_SOP value="true" />
    <vif:Product_Type_UFP_SOP value="1">PDUSB Hub</vif:Product_Type_UFP_SOP>
    <vif:Product_Type_DFP_SOP value="1">PDUSB Hub</vif:Product_Type_DFP_SOP>
    <vif:DFP_VDO_Port_Number value="0">0</vif:DFP_VDO_Port_Number>
    <vif:Modal_Operation_Supported_SOP value="true" />
    <vif:USB_VID_SOP value="1105">0451</vif:USB_VID_SOP>
    <vif:PID_SOP value="1">0001</vif:PID_SOP>
    <vif:bcdDevice_SOP value="5126">1406</vif:bcdDevice_SOP>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;SOP Modes-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:SVID_Fixed_SOP value="true" />
    <vif:Num_SVIDs_Min_SOP value="2">2</vif:Num_SVIDs_Min_SOP>
    <vif:Num_SVIDs_Max_SOP value="2">2</vif:Num_SVIDs_Max_SOP>
    <!--Bundle: SOPSVIDList-->
    <vif:SOPSVIDList>
      <vif:SOPSVID>
        <!--SOP SVID 1-->
        <vif:SVID_SOP value="32903">8087</vif:SVID_SOP>
        <vif:SVID_Modes_Fixed_SOP value="true" />
        <vif:SVID_Num_Modes_Min_SOP value="1">1</vif:SVID_Num_Modes_Min_SOP>
        <vif:SVID_Num_Modes_Max_SOP value="1">1</vif:SVID_Num_Modes_Max_SOP>
        <!--Bundle: SOPSVIDModeList-->
        <vif:SOPSVIDModeList>
          <vif:SOPSVIDMode>
            <!--SOP SVID Mode 1-->
            <vif:SVID_Mode_Enter_SOP value="true" />
            <vif:SVID_Mode_Recog_Value_SOP value="0">00000000</vif:SVID_Mode_Recog_Value_SOP>
          </vif:SOPSVIDMode>
        </vif:SOPSVIDModeList>
      </vif:SOPSVID>
      <vif:SOPSVID>
        <!--SOP SVID 2-->
        <vif:SVID_SOP value="65281">FF01</vif:SVID_SOP>
        <vif:SVID_Modes_Fixed_SOP value="true" />
        <vif:SVID_Num_Modes_Min_SOP value="1">1</vif:SVID_Num_Modes_Min_SOP>
        <vif:SVID_Num_Modes_Max_SOP value="1">1</vif:SVID_Num_Modes_Max_SOP>
        <!--Bundle: SOPSVIDModeList-->
        <vif:SOPSVIDModeList>
          <vif:SOPSVIDMode>
            <!--SOP SVID Mode 1-->
            <vif:SVID_Mode_Enter_SOP value="true" />
            <vif:SVID_Mode_Recog_Value_SOP value="1">00000001</vif:SVID_Mode_Recog_Value_SOP>
          </vif:SOPSVIDMode>
        </vif:SOPSVIDModeList>
      </vif:SOPSVID>
    </vif:SOPSVIDList>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;AMA-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:AMA_HW_Vers value="1">1</vif:AMA_HW_Vers>
    <vif:AMA_FW_Vers value="1">1</vif:AMA_FW_Vers>
    <vif:AMA_VCONN_Reqd value="false" />
    <vif:AMA_VBUS_Reqd value="true" />
    <vif:AMA_Superspeed_Support value="3">USB 2.0 Billboard Only</vif:AMA_Superspeed_Support>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;Cable/eMarker-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:XID value="0">0</vif:XID>
    <vif:Data_Capable_As_USB_Host value="false" />
    <vif:Data_Capable_As_USB_Device value="false" />
    <vif:Product_Type value="3">Passive Cable</vif:Product_Type>
    <vif:Modal_Operation_Supported value="true" />
    <vif:USB_VID value="1105">0451</vif:USB_VID>
    <vif:PID value="1">0001</vif:PID>
    <vif:bcdDevice value="5126">1406</vif:bcdDevice>
    <vif:Cable_HW_Vers value="0">0</vif:Cable_HW_Vers>
    <vif:Cable_FW_Vers value="0">0</vif:Cable_FW_Vers>
    <vif:Type_C_To_Type_A_B_C value="3">Captive</vif:Type_C_To_Type_A_B_C>
    <vif:Type_C_To_Type_C_Capt_Vdm_V2 value="3">Captive</vif:Type_C_To_Type_C_Capt_Vdm_V2>
    <vif:Cable_Latency value="2">10ns - 20ns</vif:Cable_Latency>
    <vif:Cable_Termination_Type value="0">Both ends Passive, VCONN not required</vif:Cable_Termination_Type>
    <vif:VBUS_Through_Cable value="true" />
    <vif:Cable_VBUS_Current value="2">5A</vif:Cable_VBUS_Current>
    <vif:Cable_Superspeed_Support value="2">USB 3.1 Gen 2</vif:Cable_Superspeed_Support>
    <vif:Cable_USB_Highest_Speed value="4">USB4 Gen4</vif:Cable_USB_Highest_Speed>
    <vif:EPR_Mode_Capable value="true" />
    <vif:Max_VBUS_Voltage_Vdm_V2 value="3">50V</vif:Max_VBUS_Voltage_Vdm_V2>
    <vif:Manufacturer_Info_Supported value="true" />
    <vif:Manufacturer_Info_VID value="1105">0451</vif:Manufacturer_Info_VID>
    <vif:Manufacturer_Info_PID value="1">0001</vif:Manufacturer_Info_PID>
    <vif:Chunking_Implemented value="true" />
    <vif:Security_Msgs_Supported value="false" />
    <vif:ID_Header_Connector_Type value="3">USB Type-C® Plug</vif:ID_Header_Connector_Type>
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <!--;Cable Modes-->
    <!--;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-->
    <vif:SVID_Fixed value="true" />
    <vif:Cable_Num_SVIDs_Min value="2">2</vif:Cable_Num_SVIDs_Min>
    <vif:Cable_Num_SVIDs_Max value="2">2</vif:Cable_Num_SVIDs_Max>
    <!--Bundle: CableSVIDList-->
    <vif:CableSVIDList>
      <vif:CableSVID>
        <!--Cable SVID 1-->
        <vif:SVID value="32903">8087</vif:SVID>
        <vif:SVID_Modes_Fixed value="true" />
        <vif:SVID_Num_Modes_Min value="1">1</vif:SVID_Num_Modes_Min>
        <vif:SVID_Num_Modes_Max value="1">1</vif:SVID_Num_Modes_Max>
        <!--Bundle: CableSVIDModeList-->
        <vif:CableSVIDModeList>
          <vif:CableSVIDMode>
            <!--Cable SVID Mode 1-->
            <vif:SVID_Mode_Enter value="true" />
            <vif:SVID_Mode_Recog_Value value="1">00000001</vif:SVID_Mode_Recog_Value>
          </vif:CableSVIDMode>
        </vif:CableSVIDModeList>
      </vif:CableSVID>
      <vif:CableSVID>
        <!--Cable SVID 2-->
        <vif:SVID value="65281">FF01</vif:SVID>
        <vif:SVID_Modes_Fixed value="true" />
        <vif:SVID_Num_Modes_Min value="1">1</vif:SVID_Num_Modes_Min>
        <vif:SVID_Num_Modes_Max value="1">1</vif:SVID_Num_Modes_Max>
        <!--Bundle: CableSVIDModeList-->
        <vif:CableSVIDModeList>
          <vif:CableSVIDMode>
            <!--Cable SVID Mode 1-->
            <vif:SVID_Mode_Enter value="true" />
            <vif:SVID_Mode_Recog_Value value="1">00000001</vif:SVID_Mode_Recog_Value>
          </vif:CableSVIDMode>
        </vif:CableSVIDModeList>
      </vif:CableSVID>
    </vif:CableSVIDList>
  </vif:Component>

</vif:VIF>